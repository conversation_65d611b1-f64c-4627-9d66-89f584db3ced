# Excel自动化爬取功能使用说明

## 🎯 功能概述

Excel自动化爬取功能是微信公众号爬虫工具集v2.5的新特性，它可以：

1. 从Excel文件读取待爬取的公众号信息
2. 自动打开微信文章链接
3. 自动刷新页面触发网络请求
4. 自动抓取Cookie信息
5. 批量爬取所有公众号的文章数据

## 📋 使用步骤

### 第一步：准备Excel文件

1. 运行程序选择功能3，程序会自动创建 `target_articles.xlsx` 示例文件
2. 打开Excel文件，按以下格式填写数据：

| 公众号名称 | 示例文章链接 | 备注 |
|-----------|-------------|------|
| 南京发布 | https://mp.weixin.qq.com/s/xxxxxxxxxx | 任意一篇该公众号的文章链接 |
| 人民日报 | https://mp.weixin.qq.com/s/yyyyyyyyyy | 用于获取该公众号的Cookie |
| 新华社 | https://mp.weixin.qq.com/s/zzzzzzzzzz | 链接必须是完整的微信文章URL |

**重要说明：**
- `示例文章链接` 列必须填写该公众号的任意一篇文章的完整URL
- 链接格式必须是：`https://mp.weixin.qq.com/s/xxxxxxxxxx`
- 这个链接仅用于获取该公众号的Cookie，不会影响后续爬取的文章范围

### 第二步：配置代理设置

在运行自动化爬取前，请确保：

1. **浏览器代理设置**：
   - 代理服务器：`127.0.0.1`
   - 端口：`8080`
   - 协议：HTTP

2. **推荐的浏览器代理配置方法**：
   - Chrome：设置 → 高级 → 系统 → 打开代理设置
   - Edge：设置 → 系统 → 打开代理设置
   - Firefox：设置 → 网络设置 → 手动代理配置

### 第三步：运行自动化流程

1. 运行 `python main_enhanced.py`
2. 选择功能 `3. 自动化Excel链接爬取`
3. 程序会自动执行以下步骤：

#### 自动化流程详解：

**步骤1：启动Cookie抓取器**
- 程序在后台启动mitmproxy代理服务器
- 开始监听端口8080的网络请求

**步骤2：自动打开文章链接**
- 程序读取Excel中第一个公众号的示例文章链接
- 使用默认浏览器自动打开该链接

**步骤3：自动刷新页面**
- 程序会自动刷新浏览器页面3次
- 每次刷新间隔3秒
- 触发微信文章的网络请求以获取Cookie

**步骤4：等待Cookie抓取**
- 程序监听网络请求，等待Cookie数据
- 最多等待60秒
- 成功获取Cookie后进入下一步

**步骤5：验证Cookie**
- 验证获取到的Cookie是否有效
- 显示__biz和appmsg_token等关键信息

**步骤6：批量爬取**
- 使用获取到的Cookie批量爬取所有公众号
- 每个公众号爬取2页，每页5篇文章
- 获取最近30天内的文章数据

## ⚠️ 注意事项

### 环境要求
- Python 3.7+
- 已安装所有依赖包：`pip install -r requirements.txt`
- Windows系统（uiautomation库限制）

### 浏览器要求
- 支持Chrome、Edge、Firefox
- 必须正确配置代理设置
- 建议使用Chrome浏览器以获得最佳兼容性

### 网络要求
- 稳定的网络连接
- 能够正常访问微信公众号文章
- 代理端口8080未被其他程序占用

### 使用限制
- 请遵守微信公众平台的使用条款
- 建议设置合理的爬取间隔，避免频繁请求
- 仅用于学习和研究目的

## 🔧 故障排除

### 常见问题及解决方案

**1. Cookie抓取失败**
- 检查浏览器代理设置是否正确
- 确认代理端口8080未被占用
- 手动刷新浏览器页面几次
- 检查防火墙是否阻止了代理连接

**2. 浏览器无法自动刷新**
- 确保浏览器窗口处于活动状态
- 手动点击浏览器窗口使其获得焦点
- 如果自动刷新失败，可以手动按F5刷新

**3. Excel文件读取失败**
- 检查Excel文件格式是否正确
- 确保必要的列名存在：`公众号名称`、`示例文章链接`
- 检查文章链接格式是否正确

**4. 批量爬取失败**
- 检查Cookie是否有效
- 确认网络连接稳定
- 适当增加请求间隔时间

## 📊 输出结果

成功完成后，程序会生成以下文件：

1. **Excel文件**：`./data/readnum_batch/readnum_batch_YYYYMMDD_HHMMSS.xlsx`
   - 包含所有爬取到的文章数据
   - 字段包括：标题、URL、阅读量、点赞数、分享数、发布时间等

2. **JSON文件**：`./data/readnum_batch/readnum_batch_YYYYMMDD_HHMMSS.json`
   - 与Excel相同的数据，JSON格式
   - 便于程序化处理

## 🚀 高级用法

### 自定义爬取参数

可以修改 `excel_auto_crawler.py` 中的以下参数：

```python
# 在 batch_crawl_all_accounts 方法中
results = self.spider.batch_crawl_readnum(
    max_pages=2,        # 每个公众号爬取页数
    articles_per_page=5, # 每页文章数
    days_back=30        # 爬取多少天内的文章
)
```

### 自定义延迟设置

```python
# 文章间延迟
delay = 10  # 秒

# 页面刷新间隔
interval = 3  # 秒

# 刷新次数
refresh_count = 3
```

## 📞 技术支持

如果遇到问题，请：

1. 查看控制台输出的详细错误信息
2. 检查 `wechat_keys.txt` 文件是否生成
3. 确认所有依赖包已正确安装
4. 参考本文档的故障排除部分

---

**版本信息**：微信公众号爬虫工具集 v2.5  
**更新时间**：2025-08-01  
**兼容性**：Windows 10/11, Python 3.7+
