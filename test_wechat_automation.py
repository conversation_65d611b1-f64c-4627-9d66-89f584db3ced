# coding:utf-8
# test_wechat_automation.py
"""
测试微信自动化功能
专门测试在微信内置浏览器中打开和刷新微信文章
"""

import time
import os

# 尝试导入uiautomation
try:
    import uiautomation as auto
    UI_AUTOMATION_AVAILABLE = True
    print("✅ uiautomation库已安装")
except ImportError:
    UI_AUTOMATION_AVAILABLE = False
    print("❌ uiautomation库未安装")
    print("💡 请先安装：pip install uiautomation")

def find_wechat_window():
    """查找微信PC版窗口"""
    if not UI_AUTOMATION_AVAILABLE:
        return None
        
    try:
        print("🔍 正在查找微信窗口...")
        
        # 尝试多种方式查找微信窗口
        wechat_patterns = [
            # 微信主窗口
            {'ClassName': 'WeChatMainWndForPC', 'Name': ''},
            {'ClassName': 'ChatWnd', 'Name': ''},
            # 通过窗口标题查找
            {'ClassName': '', 'Name': '微信'},
            {'ClassName': '', 'SubName': '微信'},
        ]
        
        for pattern in wechat_patterns:
            try:
                if pattern['ClassName'] and pattern['Name']:
                    window = auto.WindowControl(
                        searchDepth=1, 
                        ClassName=pattern['ClassName'],
                        Name=pattern['Name']
                    )
                elif pattern['ClassName']:
                    window = auto.WindowControl(
                        searchDepth=1, 
                        ClassName=pattern['ClassName']
                    )
                elif pattern['Name']:
                    window = auto.WindowControl(
                        searchDepth=1, 
                        Name=pattern['Name']
                    )
                elif pattern['SubName']:
                    window = auto.WindowControl(
                        searchDepth=1, 
                        SubName=pattern['SubName']
                    )
                else:
                    continue
                
                if window.Exists(2):
                    print(f"✅ 找到微信窗口: {pattern}")
                    return window
                    
            except Exception as e:
                continue
        
        # 如果上述方法都失败，尝试枚举所有窗口查找微信
        print("🔍 尝试枚举所有窗口查找微信...")
        try:
            desktop = auto.GetRootControl()
            for window in desktop.GetChildren():
                window_name = window.Name
                if window_name and ('微信' in window_name or 'WeChat' in window_name):
                    print(f"✅ 通过枚举找到微信窗口: {window_name}")
                    return window
        except:
            pass
            
        return None
        
    except Exception as e:
        print(f"⚠️ 查找微信窗口时出错: {e}")
        return None

def test_wechat_automation():
    """测试微信自动化功能"""
    print("🧪 测试微信自动化功能")
    print("="*50)
    
    if not UI_AUTOMATION_AVAILABLE:
        print("❌ 无法测试，uiautomation库未安装")
        return False
    
    # 查找微信窗口
    wechat_window = find_wechat_window()
    if not wechat_window:
        print("❌ 未找到微信窗口")
        print("💡 请确保微信PC版已打开")
        return False
    
    print("✅ 找到微信窗口，开始测试...")
    
    # 激活微信窗口
    try:
        wechat_window.SetActive()
        time.sleep(2)
        print("✅ 微信窗口已激活")
    except Exception as e:
        print(f"❌ 激活微信窗口失败: {e}")
        return False
    
    # 测试基本操作
    print("\n🧪 测试基本UI操作...")
    
    # 测试查找输入框
    try:
        input_box = auto.EditControl(searchDepth=3)
        if input_box.Exists(2):
            print("✅ 找到输入框")
            
            # 测试输入文本
            test_text = "测试自动化功能"
            input_box.Click()
            time.sleep(0.5)
            input_box.SendKeys(test_text)
            time.sleep(1)
            
            # 清空输入框
            auto.SendKeys('{Ctrl}a')
            auto.SendKeys('{Delete}')
            print("✅ 输入框操作测试成功")
        else:
            print("⚠️ 未找到输入框")
    except Exception as e:
        print(f"⚠️ 输入框测试失败: {e}")
    
    # 测试查找文件传输助手
    try:
        file_transfer = auto.TextControl(searchDepth=3, Name='文件传输助手')
        if file_transfer.Exists(2):
            print("✅ 找到文件传输助手")
        else:
            print("⚠️ 未找到文件传输助手")
    except Exception as e:
        print(f"⚠️ 文件传输助手测试失败: {e}")
    
    print("\n✅ 微信自动化基本功能测试完成")
    return True

def test_link_automation():
    """测试链接自动化功能"""
    print("\n🔗 测试链接自动化功能")
    print("="*30)
    
    if not UI_AUTOMATION_AVAILABLE:
        print("❌ 无法测试，uiautomation库未安装")
        return False
    
    # 示例微信文章链接
    test_url = "https://mp.weixin.qq.com/s/example_test_link"
    
    print(f"🔗 测试链接: {test_url}")
    print("💡 这将测试在微信中发送和点击链接的功能")
    
    # 查找微信窗口
    wechat_window = find_wechat_window()
    if not wechat_window:
        print("❌ 未找到微信窗口")
        return False
    
    # 激活微信窗口
    wechat_window.SetActive()
    time.sleep(2)
    
    try:
        # 查找输入框
        input_box = auto.EditControl(searchDepth=3)
        if input_box.Exists(2):
            print("✅ 找到输入框，准备发送测试链接...")
            
            # 点击输入框
            input_box.Click()
            time.sleep(0.5)
            
            # 清空输入框
            auto.SendKeys('{Ctrl}a')
            time.sleep(0.2)
            
            # 输入链接
            input_box.SendKeys(test_url)
            time.sleep(1)
            
            print("✅ 链接已输入到输入框")
            print("💡 注意：这只是测试，不会实际发送链接")
            
            # 清空输入框（不实际发送）
            auto.SendKeys('{Ctrl}a')
            auto.SendKeys('{Delete}')
            
            print("✅ 链接自动化测试完成")
            return True
        else:
            print("❌ 未找到输入框")
            return False
            
    except Exception as e:
        print(f"❌ 链接自动化测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 微信自动化功能测试工具")
    print("="*50)
    print("💡 此工具用于测试在微信PC版中的自动化操作")
    print("⚠️ 请确保微信PC版已打开")
    print()
    
    # 基本功能测试
    if test_wechat_automation():
        print("\n" + "="*50)
        
        # 询问是否测试链接功能
        choice = input("是否测试链接自动化功能？(y/N): ").strip().lower()
        if choice == 'y':
            test_link_automation()
    
    print("\n" + "="*50)
    print("🎯 测试总结：")
    print("1. 如果找到了微信窗口，说明uiautomation可以控制微信")
    print("2. 如果找到了输入框，说明可以自动输入链接")
    print("3. 接下来可以在实际的Excel自动化爬取中使用这些功能")
    print("="*50)

if __name__ == "__main__":
    main()
