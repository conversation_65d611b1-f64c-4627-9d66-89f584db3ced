# coding:utf-8
# excel_auto_crawler.py
"""
Excel自动化爬取模块
功能：从Excel文件读取微信文章链接，使用uiautomation自动打开链接并触发Cookie抓取
"""

import os
import time
import pandas as pd
import webbrowser
import subprocess
from datetime import datetime
from read_cookie import ReadCookie
from batch_readnum_spider import BatchReadnumSpider

# 尝试导入uiautomation，如果失败则使用简化版本
try:
    import uiautomation as auto
    UI_AUTOMATION_AVAILABLE = True
except ImportError:
    UI_AUTOMATION_AVAILABLE = False
    print("⚠️ uiautomation库未安装，将使用简化版自动化功能")

# 导入微信浏览器自动化模块
try:
    from wechat_browser_automation import WeChatBrowserAutomation
    WECHAT_AUTOMATION_AVAILABLE = True
except ImportError:
    WECHAT_AUTOMATION_AVAILABLE = False
    print("⚠️ 微信浏览器自动化模块导入失败")


class ExcelAutoCrawler:
    """Excel自动化爬取器"""
    
    def __init__(self, excel_file="target_articles.xlsx"):
        self.excel_file = excel_file
        self.articles_data = []
        self.cookie_reader = ReadCookie()
        self.spider = BatchReadnumSpider()

        # 初始化微信浏览器自动化
        if WECHAT_AUTOMATION_AVAILABLE:
            self.wechat_automation = WeChatBrowserAutomation()
        else:
            self.wechat_automation = None

        print(f"📊 Excel自动化爬取器已初始化")
        print(f"📁 Excel文件: {excel_file}")
        if UI_AUTOMATION_AVAILABLE:
            print("✅ uiautomation库可用，支持完全自动化")
        else:
            print("⚠️ uiautomation库不可用，需要手动操作")

        if WECHAT_AUTOMATION_AVAILABLE:
            print("✅ 微信浏览器自动化模块可用")
        else:
            print("⚠️ 微信浏览器自动化模块不可用")
        
    def create_sample_excel(self):
        """创建示例Excel文件"""
        sample_data = {
            '公众号名称': [
                '钟山清风',
            ],
            '示例文章链接': [
                'https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect',
            ],
            '备注': [
                '请替换为实际的微信文章链接',
                '每行一个公众号和对应的示例文章',
                '程序会自动打开第一个链接进行Cookie抓取'
            ]
        }
        
        df = pd.DataFrame(sample_data)
        df.to_excel(self.excel_file, index=False, engine='openpyxl')
        print(f"📝 已创建示例Excel文件: {self.excel_file}")
        print("请编辑此文件，填入实际的公众号名称和文章链接")
        return True
        
    def read_excel_data(self):
        """从Excel文件读取数据"""
        if not os.path.exists(self.excel_file):
            print(f"❌ Excel文件不存在: {self.excel_file}")
            print("正在创建示例文件...")
            self.create_sample_excel()
            return []
            
        try:
            df = pd.read_excel(self.excel_file, engine='openpyxl')
            
            # 检查必要的列
            required_columns = ['公众号名称', '示例文章链接']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                print(f"❌ Excel文件缺少必要的列: {missing_columns}")
                print("请确保Excel文件包含以下列：公众号名称, 示例文章链接")
                return []
                
            # 过滤有效数据
            valid_data = []
            for index, row in df.iterrows():
                account_name = str(row['公众号名称']).strip()
                article_url = str(row['示例文章链接']).strip()
                
                if account_name and article_url and 'mp.weixin.qq.com' in article_url:
                    valid_data.append({
                        'account_name': account_name,
                        'article_url': article_url,
                        'row_index': index + 1
                    })
                    
            print(f"✅ 从Excel读取到 {len(valid_data)} 条有效数据")
            return valid_data
            
        except Exception as e:
            print(f"❌ 读取Excel文件失败: {e}")
            return []
    
    def open_article_in_wechat(self, article_url, wait_seconds=10):
        """使用uiautomation自动打开微信并在微信内置浏览器中打开文章链接"""
        try:
            print(f"📱 正在使用uiautomation自动打开微信...")
            print(f"🔗 文章链接: {article_url}")
            print("💡 关键：只有在微信内置浏览器中才能抓取到正确的网络包")

            if not UI_AUTOMATION_AVAILABLE:
                print("❌ uiautomation库未安装，无法自动化操作")
                print("💡 请手动执行以下步骤：")
                print("1. 打开微信PC版")
                print("2. 将文章链接发送给文件传输助手")
                print("3. 点击链接在微信内置浏览器中打开")
                print("4. 刷新页面几次触发网络请求")
                input("完成上述步骤后按回车继续...")
                return True

            # 第一步：查找并激活微信窗口
            wechat_window = self.find_wechat_window()
            if not wechat_window:
                print("❌ 未找到微信窗口，请先打开微信PC版")
                print("💡 请手动打开微信后重试")
                return False

            print("✅ 找到微信窗口，正在激活...")
            wechat_window.SetActive()
            time.sleep(2)

            # 第二步：自动化操作微信
            success = self.automate_wechat_open_link(article_url)
            if success:
                print(f"⏳ 等待 {wait_seconds} 秒让微信内置浏览器加载页面...")
                time.sleep(wait_seconds)
                return True
            else:
                print("⚠️ 自动化操作失败，请手动操作")
                return False

        except Exception as e:
            print(f"❌ 在微信中打开链接失败: {e}")
            print("💡 请手动在微信中打开文章链接")
            return False

    def find_wechat_window(self):
        """查找微信PC版窗口"""
        if not UI_AUTOMATION_AVAILABLE:
            return None

        try:
            # 尝试查找微信窗口的不同方式
            wechat_patterns = [
                # 微信主窗口
                {'ClassName': 'WeChatMainWndForPC', 'Name': ''},
                {'ClassName': 'ChatWnd', 'Name': ''},
                # 通过窗口标题查找
                {'ClassName': '', 'Name': '微信'},
                {'ClassName': '', 'SubName': '微信'},
            ]

            for pattern in wechat_patterns:
                try:
                    if pattern['ClassName'] and pattern['Name']:
                        window = auto.WindowControl(
                            searchDepth=1,
                            ClassName=pattern['ClassName'],
                            Name=pattern['Name']
                        )
                    elif pattern['ClassName']:
                        window = auto.WindowControl(
                            searchDepth=1,
                            ClassName=pattern['ClassName']
                        )
                    elif pattern['Name']:
                        window = auto.WindowControl(
                            searchDepth=1,
                            Name=pattern['Name']
                        )
                    elif pattern['SubName']:
                        window = auto.WindowControl(
                            searchDepth=1,
                            SubName=pattern['SubName']
                        )
                    else:
                        continue

                    if window.Exists(2):
                        print(f"✅ 找到微信窗口: {pattern}")
                        return window

                except Exception as e:
                    continue

            # 如果上述方法都失败，尝试枚举所有窗口查找微信
            try:
                desktop = auto.GetRootControl()
                for window in desktop.GetChildren():
                    window_name = window.Name
                    if window_name and ('微信' in window_name or 'WeChat' in window_name):
                        print(f"✅ 通过枚举找到微信窗口: {window_name}")
                        return window
            except:
                pass

            return None

        except Exception as e:
            print(f"⚠️ 查找微信窗口时出错: {e}")
            return None

    def automate_wechat_open_link(self, article_url):
        """自动化操作微信打开链接"""
        if not UI_AUTOMATION_AVAILABLE:
            return False

        try:
            print("🤖 开始自动化操作微信...")

            # 方法1：尝试查找文件传输助手
            print("📁 尝试查找文件传输助手...")
            try:
                # 查找文件传输助手或任何聊天窗口
                file_transfer = auto.TextControl(searchDepth=3, Name='文件传输助手')
                if file_transfer.Exists(2):
                    print("✅ 找到文件传输助手")
                    file_transfer.Click()
                    time.sleep(1)
                else:
                    print("⚠️ 未找到文件传输助手，尝试其他方法")
                    # 尝试点击任何聊天窗口
                    chat_list = auto.ListControl(searchDepth=3)
                    if chat_list.Exists(2):
                        chat_items = chat_list.GetChildren()
                        if chat_items:
                            chat_items[0].Click()
                            time.sleep(1)
            except Exception as e:
                print(f"⚠️ 查找聊天窗口失败: {e}")

            # 方法2：直接在输入框中输入链接
            print("⌨️ 尝试在输入框中输入链接...")
            try:
                # 查找输入框
                input_box = auto.EditControl(searchDepth=3)
                if input_box.Exists(2):
                    print("✅ 找到输入框")
                    input_box.Click()
                    time.sleep(0.5)

                    # 清空输入框
                    auto.SendKeys('{Ctrl}a')
                    time.sleep(0.2)

                    # 输入链接
                    input_box.SendKeys(article_url)
                    time.sleep(1)

                    # 发送消息
                    auto.SendKeys('{Enter}')
                    time.sleep(2)

                    print("✅ 链接已发送")

                    # 尝试点击刚发送的链接
                    print("🔗 尝试点击链接...")
                    link_element = auto.TextControl(searchDepth=3, SubName='mp.weixin.qq.com')
                    if link_element.Exists(3):
                        print("✅ 找到链接，正在点击...")
                        link_element.Click()
                        time.sleep(3)
                        return True
                    else:
                        print("⚠️ 未找到可点击的链接")

                else:
                    print("❌ 未找到输入框")

            except Exception as e:
                print(f"⚠️ 输入链接失败: {e}")

            # 方法3：使用剪贴板
            print("📋 尝试使用剪贴板方法...")
            try:
                import pyperclip
                # 将链接复制到剪贴板
                pyperclip.copy(article_url)

                # 在微信中粘贴
                auto.SendKeys('{Ctrl}v')
                time.sleep(1)
                auto.SendKeys('{Enter}')
                time.sleep(2)

                print("✅ 使用剪贴板发送链接")
                return True

            except ImportError:
                print("⚠️ pyperclip库未安装，跳过剪贴板方法")
            except Exception as e:
                print(f"⚠️ 剪贴板方法失败: {e}")

            return False

        except Exception as e:
            print(f"❌ 自动化操作微信失败: {e}")
            return False
    
    def auto_refresh_wechat_browser(self, refresh_count=5, interval=3):
        """在微信内置浏览器中自动刷新页面以触发网络请求"""
        try:
            print(f"🔄 开始在微信内置浏览器中自动刷新页面 {refresh_count} 次...")
            print("💡 关键：只有在微信内置浏览器中刷新才能触发正确的网络请求")

            if not UI_AUTOMATION_AVAILABLE:
                print("❌ uiautomation库未安装，无法自动刷新")
                print("📝 请手动执行以下操作：")
                print("1. 确保文章在微信内置浏览器中打开")
                print("2. 手动刷新页面多次")
                for i in range(refresh_count):
                    print(f"🔄 请手动刷新微信浏览器页面（第 {i+1}/{refresh_count} 次）")
                    input("刷新完成后按回车继续...")
                return True

            # 等待用户确认
            print("⚠️ 请确认：")
            print("1. 文章已在微信内置浏览器中打开")
            print("2. 微信PC版已配置代理 127.0.0.1:8080")
            print("3. Cookie抓取器正在运行")
            input("确认无误后按回车开始自动刷新...")

            # 查找微信浏览器窗口
            wechat_browser_window = self.find_wechat_browser_window()
            if not wechat_browser_window:
                print("⚠️ 未找到微信浏览器窗口，尝试查找微信主窗口...")
                wechat_window = self.find_wechat_window()
                if wechat_window:
                    wechat_browser_window = wechat_window
                else:
                    print("❌ 未找到微信窗口，无法自动刷新")
                    print("💡 请手动在微信中刷新页面")
                    return False

            # 执行自动刷新
            for i in range(refresh_count):
                print(f"🔄 第 {i+1}/{refresh_count} 次自动刷新微信浏览器")

                try:
                    # 激活微信浏览器窗口
                    wechat_browser_window.SetActive()
                    time.sleep(1)

                    # 尝试多种刷新方法
                    refresh_methods = [
                        ('F5', '{F5}'),
                        ('Ctrl+R', '{Ctrl}r'),
                        ('右键刷新', None)  # 特殊处理
                    ]

                    for method_name, key_combo in refresh_methods:
                        try:
                            if key_combo:
                                print(f"   🔄 尝试 {method_name} 刷新...")
                                auto.SendKeys(key_combo)
                                time.sleep(1)
                                break
                            else:
                                # 右键刷新方法
                                print(f"   🔄 尝试右键菜单刷新...")
                                # 右键点击页面
                                auto.Click(button='right')
                                time.sleep(0.5)
                                # 查找刷新选项
                                refresh_menu = auto.MenuItemControl(searchDepth=2, Name='刷新')
                                if refresh_menu.Exists(1):
                                    refresh_menu.Click()
                                    time.sleep(1)
                                    break
                                else:
                                    # 按ESC关闭右键菜单
                                    auto.SendKeys('{Esc}')
                        except Exception as e:
                            print(f"   ⚠️ {method_name} 方法失败: {e}")
                            continue

                    print(f"   ✅ 第 {i+1} 次刷新完成")

                    # 等待页面加载
                    print(f"   ⏳ 等待 {interval} 秒让页面加载...")
                    time.sleep(interval)

                    # 检查网络请求
                    if i == 0:
                        print("   💡 第一次刷新完成，Cookie抓取器应该开始捕获微信请求")
                    elif i == 2:
                        print("   💡 多次刷新有助于触发更多网络请求")

                except Exception as e:
                    print(f"   ❌ 第 {i+1} 次刷新失败: {e}")
                    # 尝试重新找到微信窗口
                    wechat_browser_window = self.find_wechat_browser_window()
                    if not wechat_browser_window:
                        wechat_browser_window = self.find_wechat_window()
                    if not wechat_browser_window:
                        print("   ❌ 无法重新找到微信窗口")
                        break
                    continue

            print("✅ 微信浏览器自动刷新完成")
            print("💡 如果Cookie抓取器没有捕获到请求，请手动在微信中再刷新几次")
            return True

        except Exception as e:
            print(f"❌ 微信浏览器自动刷新失败: {e}")
            print("💡 请手动在微信内置浏览器中刷新页面几次以触发网络请求")
            return False

    def find_wechat_browser_window(self):
        """查找微信内置浏览器窗口"""
        if not UI_AUTOMATION_AVAILABLE:
            return None

        try:
            # 尝试查找微信浏览器窗口的不同方式
            browser_patterns = [
                # 微信浏览器窗口
                {'ClassName': 'Chrome_WidgetWin_1', 'SubName': '微信'},
                {'ClassName': 'CefWebViewWnd', 'Name': ''},
                # 通过标题查找
                {'ClassName': '', 'SubName': 'mp.weixin.qq.com'},
                {'ClassName': '', 'SubName': '微信公众平台'},
            ]

            for pattern in browser_patterns:
                try:
                    if pattern['ClassName'] and pattern['SubName']:
                        window = auto.WindowControl(
                            searchDepth=1,
                            ClassName=pattern['ClassName'],
                            SubName=pattern['SubName']
                        )
                    elif pattern['ClassName']:
                        window = auto.WindowControl(
                            searchDepth=1,
                            ClassName=pattern['ClassName']
                        )
                    elif pattern['SubName']:
                        window = auto.WindowControl(
                            searchDepth=1,
                            SubName=pattern['SubName']
                        )
                    else:
                        continue

                    if window.Exists(2):
                        print(f"✅ 找到微信浏览器窗口: {pattern}")
                        return window

                except Exception as e:
                    continue

            return None

        except Exception as e:
            print(f"⚠️ 查找微信浏览器窗口时出错: {e}")
            return None
    
    def wait_for_cookie_capture(self, timeout=60):
        """等待Cookie抓取完成"""
        print(f"⏳ 等待Cookie抓取完成（最多等待{timeout}秒）...")
        print("💡 提示：Cookie抓取器正在监听网络请求，请确保：")
        print("   1. 代理设置为 127.0.0.1:8080")
        print("   2. 浏览器已正确配置代理")
        print("   3. 页面正在加载微信文章")

        # 记录开始时的Cookie状态
        initial_result = self.cookie_reader.get_latest_cookies()
        initial_timestamp = initial_result['timestamp'] if initial_result else None

        start_time = time.time()
        check_count = 0

        while time.time() - start_time < timeout:
            # 检查是否有新的Cookie数据
            result = self.cookie_reader.get_latest_cookies()

            if result:
                # 检查是否是新的Cookie数据
                if initial_timestamp is None or result['timestamp'] != initial_timestamp:
                    print(f"\n✅ 检测到新的Cookie数据！")
                    print(f"   时间戳: {result['timestamp']}")
                    return True

            check_count += 1
            if check_count % 5 == 0:  # 每10秒显示一次状态
                elapsed = int(time.time() - start_time)
                print(f"\n⏳ 已等待 {elapsed}秒，继续监听...")
            else:
                print(".", end="", flush=True)

            time.sleep(2)

        print(f"\n⚠️ 在{timeout}秒内未检测到新的Cookie数据")
        print("💡 建议：")
        print("   1. 检查代理设置是否正确")
        print("   2. 手动刷新浏览器页面")
        print("   3. 确认Cookie抓取器正在运行")
        return False
    
    def start_cookie_extractor_background(self):
        """在后台启动Cookie抓取器"""
        try:
            print("🔧 启动后台Cookie抓取器...")
            
            # 启动Cookie抓取器
            success = self.cookie_reader.start_cookie_extractor(timeout=5)
            
            if success:
                print("✅ Cookie抓取器已启动")
                return True
            else:
                print("❌ Cookie抓取器启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动Cookie抓取器失败: {e}")
            return False
    
    def auto_crawl_from_excel(self):
        """从Excel自动化爬取流程"""
        print("\n" + "="*60)
        print("🚀 Excel自动化爬取流程启动")
        print("="*60)
        
        # 1. 读取Excel数据
        excel_data = self.read_excel_data()
        if not excel_data:
            return False
            
        # 显示读取到的数据
        print("\n📋 待处理的公众号列表:")
        for i, item in enumerate(excel_data, 1):
            print(f"{i}. {item['account_name']} - {item['article_url'][:50]}...")
            
        # 2. 启动Cookie抓取器
        print(f"\n🔧 第一步：启动Cookie抓取器")
        if not self.start_cookie_extractor_background():
            print("❌ Cookie抓取器启动失败，无法继续")
            return False
            
        # 3. 使用微信浏览器自动化打开和刷新第一个文章链接
        first_article = excel_data[0]
        print(f"\n📱 第二步：使用微信内置浏览器自动化打开第一个文章链接")
        print(f"📰 公众号: {first_article['account_name']}")
        print("💡 关键：必须在微信内置浏览器中打开才能抓取到正确的网络包")

        if self.wechat_automation:
            # 使用专门的微信浏览器自动化模块
            if not self.wechat_automation.open_and_refresh_article(
                first_article['article_url'],
                refresh_count=5
            ):
                print("❌ 微信浏览器自动化失败")
                return False
        else:
            # 回退到原有方法
            if not self.open_article_in_wechat(first_article['article_url']):
                print("❌ 在微信中打开文章失败")
                return False

            # 在微信内置浏览器中自动刷新页面
            print(f"\n🔄 第三步：在微信内置浏览器中自动刷新页面触发网络请求")
            if not self.auto_refresh_wechat_browser(refresh_count=5, interval=3):
                print("❌ 微信浏览器自动刷新失败")
                return False
            
        # 5. 等待Cookie抓取
        print(f"\n⏳ 第四步：等待Cookie抓取完成")
        if self.wait_for_cookie_capture(timeout=60):
            print("✅ Cookie抓取成功！")
        else:
            print("⚠️ Cookie抓取超时，请手动检查")
            
        # 6. 验证Cookie
        print(f"\n🔍 第五步：验证Cookie有效性")
        cookie_result = self.cookie_reader.get_latest_cookies()
        
        if cookie_result:
            print("✅ Cookie验证成功:")
            print(f"   __biz: {cookie_result['biz']}")
            print(f"   appmsg_token: {cookie_result['appmsg_token'][:20]}...")
            print(f"   解析时间: {cookie_result['timestamp']}")
            
            # 7. 开始批量爬取
            print(f"\n🚀 第六步：开始批量爬取所有公众号")
            return self.batch_crawl_all_accounts(excel_data)
        else:
            print("❌ Cookie验证失败，请检查网络和代理设置")
            return False
    
    def batch_crawl_all_accounts(self, excel_data):
        """批量爬取所有公众号"""
        try:
            print(f"📊 开始批量爬取 {len(excel_data)} 个公众号...")
            
            all_results = []
            
            for i, account_info in enumerate(excel_data, 1):
                print(f"\n{'='*50}")
                print(f"📰 处理第 {i}/{len(excel_data)} 个公众号: {account_info['account_name']}")
                
                # 这里可以根据需要调整爬取参数
                results = self.spider.batch_crawl_readnum(
                    max_pages=2,  # 每个公众号爬取2页
                    articles_per_page=5,  # 每页5篇文章
                    days_back=30  # 30天内的文章
                )
                
                if results:
                    # 为每个结果添加公众号信息
                    for result in results:
                        result['source_account'] = account_info['account_name']
                        result['source_url'] = account_info['article_url']
                    
                    all_results.extend(results)
                    print(f"✅ {account_info['account_name']} 完成，获取 {len(results)} 篇文章")
                else:
                    print(f"⚠️ {account_info['account_name']} 未获取到数据")
                
                # 公众号间延迟
                if i < len(excel_data):
                    delay = 10
                    print(f"⏳ 公众号间延迟 {delay} 秒...")
                    time.sleep(delay)
            
            # 保存所有结果
            if all_results:
                self.spider.articles_data = all_results
                excel_file = self.spider.save_to_excel()
                json_file = self.spider.save_to_json()
                
                print(f"\n🎉 批量爬取完成！")
                print(f"📊 总共获取 {len(all_results)} 篇文章")
                if excel_file:
                    print(f"📊 Excel文件: {excel_file}")
                if json_file:
                    print(f"💾 JSON文件: {json_file}")
                    
                return True
            else:
                print("❌ 未获取到任何数据")
                return False
                
        except Exception as e:
            print(f"❌ 批量爬取过程出错: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数示例"""
    crawler = ExcelAutoCrawler()
    
    print("🚀 Excel自动化爬取器")
    print("="*50)
    
    # 检查Excel文件是否存在
    if not os.path.exists(crawler.excel_file):
        print("📝 首次使用，创建示例Excel文件...")
        crawler.create_sample_excel()
        print("请编辑Excel文件后重新运行程序")
        return
    
    # 开始自动化爬取
    success = crawler.auto_crawl_from_excel()
    
    if success:
        print("\n✅ 自动化爬取流程完成！")
    else:
        print("\n❌ 自动化爬取流程失败")


if __name__ == "__main__":
    main()
