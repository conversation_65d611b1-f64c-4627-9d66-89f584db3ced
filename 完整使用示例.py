# coding:utf-8
# 完整使用示例.py
"""
Excel自动化爬取功能完整使用示例
演示如何从Excel读取链接并自动化爬取微信公众号
"""

import os
import pandas as pd
from excel_auto_crawler import ExcelAutoCrawler

def create_example_excel():
    """创建一个真实的示例Excel文件"""
    print("📝 创建示例Excel文件...")
    
    # 真实的示例数据（请替换为实际的微信文章链接）
    sample_data = {
        '公众号名称': [
            '南京发布',
            '人民日报',
            '新华社',
            '央视新闻'
        ],
        '示例文章链接': [
            'https://mp.weixin.qq.com/s/example_nanjing_link',
            'https://mp.weixin.qq.com/s/example_rmrb_link', 
            'https://mp.weixin.qq.com/s/example_xinhua_link',
            'https://mp.weixin.qq.com/s/example_cctv_link'
        ],
        '备注': [
            '南京市官方发布平台',
            '人民日报官方微信',
            '新华社官方微信',
            '央视新闻官方微信'
        ]
    }
    
    df = pd.DataFrame(sample_data)
    filename = "example_target_articles.xlsx"
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ 示例Excel文件已创建: {filename}")
    print("\n📋 文件内容:")
    print(df.to_string(index=False))
    
    return filename

def show_usage_steps():
    """显示详细的使用步骤"""
    print("\n" + "="*60)
    print("📖 Excel自动化爬取使用步骤")
    print("="*60)
    
    print("\n🔧 第一步：环境准备")
    print("1. 安装uiautomation库：pip install uiautomation")
    print("2. 确保已安装所有依赖：pip install -r requirements.txt")
    
    print("\n📝 第二步：准备Excel文件")
    print("1. 创建或编辑 target_articles.xlsx 文件")
    print("2. 填入公众号名称和对应的示例文章链接")
    print("3. 链接格式：https://mp.weixin.qq.com/s/xxxxxxxxxx")
    
    print("\n🌐 第三步：配置浏览器代理")
    print("1. 打开浏览器设置")
    print("2. 配置HTTP代理：127.0.0.1:8080")
    print("3. 确保代理设置生效")
    
    print("\n🚀 第四步：运行自动化流程")
    print("1. 运行：python main_enhanced.py")
    print("2. 选择功能3：自动化Excel链接爬取")
    print("3. 程序将自动执行以下步骤：")
    print("   - 启动Cookie抓取器")
    print("   - 打开第一个文章链接")
    print("   - 自动刷新页面抓取Cookie")
    print("   - 批量爬取所有公众号")
    
    print("\n⚠️ 重要注意事项：")
    print("1. 必须使用uiautomation在浏览器中打开和刷新微信文章")
    print("2. 只有这样才能触发正确的网络请求来抓取Cookie")
    print("3. 手动打开链接可能无法抓取到有效的Cookie")
    print("4. 确保浏览器代理配置正确")

def test_excel_functionality():
    """测试Excel功能"""
    print("\n🧪 测试Excel功能...")
    
    # 创建示例文件
    example_file = create_example_excel()
    
    # 测试读取
    try:
        crawler = ExcelAutoCrawler(excel_file=example_file)
        data = crawler.read_excel_data()
        
        if data:
            print(f"\n✅ Excel读取测试成功，获取到 {len(data)} 条记录")
            print("\n📋 读取到的数据:")
            for i, item in enumerate(data, 1):
                print(f"{i}. {item['account_name']} - {item['article_url']}")
        else:
            print("\n❌ Excel读取测试失败")
            
    except Exception as e:
        print(f"\n❌ Excel功能测试失败: {e}")

def check_dependencies():
    """检查依赖库"""
    print("\n🔍 检查依赖库...")
    
    dependencies = [
        ('pandas', 'Excel文件处理'),
        ('openpyxl', 'Excel文件读写'),
        ('requests', 'HTTP请求'),
        ('beautifulsoup4', 'HTML解析'),
        ('uiautomation', '浏览器自动化（关键）')
    ]
    
    all_ok = True
    
    for lib_name, description in dependencies:
        try:
            __import__(lib_name)
            print(f"✅ {lib_name} - {description}")
        except ImportError:
            print(f"❌ {lib_name} - {description} (未安装)")
            all_ok = False
    
    if all_ok:
        print("\n✅ 所有依赖库检查通过")
    else:
        print("\n⚠️ 部分依赖库缺失，请安装后重试")
        print("💡 安装命令：pip install pandas openpyxl requests beautifulsoup4 uiautomation")
    
    return all_ok

def main():
    """主函数"""
    print("🚀 Excel自动化爬取功能完整使用示例")
    print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 显示使用步骤
    show_usage_steps()
    
    # 测试Excel功能
    choice = input("\n是否测试Excel功能？(y/N): ").strip().lower()
    if choice == 'y':
        test_excel_functionality()
    
    print("\n" + "="*60)
    print("💡 接下来您可以：")
    print("1. 编辑生成的Excel文件，填入真实的微信文章链接")
    print("2. 配置浏览器代理为 127.0.0.1:8080")
    print("3. 运行 python main_enhanced.py 开始自动化爬取")
    print("="*60)

if __name__ == "__main__":
    main()
