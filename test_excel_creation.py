# coding:utf-8
# test_excel_creation.py
"""
测试Excel自动化爬取器的Excel文件创建功能
"""

from excel_auto_crawler import ExcelAutoCrawler
import os

def test_excel_creation():
    """测试Excel文件创建功能"""
    print("🧪 测试Excel文件创建功能")
    print("="*50)
    
    # 创建爬取器实例
    crawler = ExcelAutoCrawler()
    
    # 如果文件已存在，先删除
    if os.path.exists(crawler.excel_file):
        os.remove(crawler.excel_file)
        print(f"🗑️ 已删除现有文件: {crawler.excel_file}")
    
    # 创建示例Excel文件
    success = crawler.create_sample_excel()
    
    if success:
        print("✅ Excel文件创建成功！")
        
        # 验证文件是否存在
        if os.path.exists(crawler.excel_file):
            print(f"✅ 文件确实存在: {crawler.excel_file}")
            
            # 尝试读取文件内容
            try:
                data = crawler.read_excel_data()
                print(f"✅ 文件读取成功，包含 {len(data)} 条记录")
                
                if data:
                    print("📋 示例数据:")
                    for i, item in enumerate(data, 1):
                        print(f"  {i}. {item['account_name']} - {item['article_url'][:50]}...")
                else:
                    print("⚠️ 文件为空或格式不正确")
                    
            except Exception as e:
                print(f"❌ 文件读取失败: {e}")
        else:
            print(f"❌ 文件创建后不存在: {crawler.excel_file}")
    else:
        print("❌ Excel文件创建失败")

if __name__ == "__main__":
    test_excel_creation()
