# coding:utf-8
# simple_excel_test.py
"""
简单的Excel创建和读取测试
"""

import pandas as pd
import os

def create_sample_excel():
    """创建示例Excel文件"""
    print("📝 创建示例Excel文件...")
    
    sample_data = {
        '公众号名称': [
            '南京发布',
            '人民日报',
            '新华社'
        ],
        '示例文章链接': [
            'https://mp.weixin.qq.com/s/example1',
            'https://mp.weixin.qq.com/s/example2',
            'https://mp.weixin.qq.com/s/example3'
        ],
        '备注': [
            '请替换为实际的微信文章链接',
            '每行一个公众号和对应的示例文章',
            '程序会自动打开第一个链接进行Cookie抓取'
        ]
    }
    
    try:
        df = pd.DataFrame(sample_data)
        filename = "target_articles.xlsx"
        df.to_excel(filename, index=False, engine='openpyxl')
        print(f"✅ Excel文件创建成功: {filename}")
        return True
    except Exception as e:
        print(f"❌ Excel文件创建失败: {e}")
        return False

def read_excel_data():
    """读取Excel文件"""
    filename = "target_articles.xlsx"
    
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return []
    
    try:
        df = pd.read_excel(filename, engine='openpyxl')
        print(f"✅ Excel文件读取成功")
        print(f"📊 数据形状: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")
        
        # 显示数据
        print("\n📄 数据内容:")
        for index, row in df.iterrows():
            print(f"  {index+1}. {row['公众号名称']} - {row['示例文章链接']}")
            
        return df.to_dict('records')
        
    except Exception as e:
        print(f"❌ Excel文件读取失败: {e}")
        return []

def main():
    print("🧪 简单Excel测试")
    print("="*50)
    
    # 创建Excel文件
    if create_sample_excel():
        # 读取Excel文件
        data = read_excel_data()
        if data:
            print(f"\n✅ 测试成功！读取到 {len(data)} 条记录")
        else:
            print("\n❌ 读取失败")
    else:
        print("\n❌ 创建失败")

if __name__ == "__main__":
    main()
