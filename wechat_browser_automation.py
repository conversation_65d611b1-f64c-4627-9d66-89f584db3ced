# coding:utf-8
# wechat_browser_automation.py
"""
微信内置浏览器自动化模块
专门用于在微信PC版中自动打开和刷新微信文章链接
"""

import time
import os
import subprocess
import threading

# 尝试导入uiautomation
try:
    import uiautomation as auto
    UI_AUTOMATION_AVAILABLE = True
except ImportError:
    UI_AUTOMATION_AVAILABLE = False

class WeChatBrowserAutomation:
    """微信内置浏览器自动化控制器"""
    
    def __init__(self):
        self.wechat_window = None
        self.browser_window = None
        
    def check_uiautomation(self):
        """检查uiautomation库是否可用"""
        if not UI_AUTOMATION_AVAILABLE:
            print("❌ uiautomation库未安装")
            print("💡 请安装：pip install uiautomation")
            return False
        
        print("✅ uiautomation库已安装")
        return True
    
    def find_wechat_window(self):
        """查找微信PC版窗口"""
        if not UI_AUTOMATION_AVAILABLE:
            return None
            
        print("🔍 正在查找微信窗口...")
        
        try:
            # 方法1：通过类名查找
            wechat_classes = [
                'WeChatMainWndForPC',
                'ChatWnd', 
                'WeUIEngine'
            ]
            
            for class_name in wechat_classes:
                try:
                    window = auto.WindowControl(searchDepth=1, ClassName=class_name)
                    if window.Exists(2):
                        print(f"✅ 通过类名找到微信窗口: {class_name}")
                        self.wechat_window = window
                        return window
                except:
                    continue
            
            # 方法2：通过窗口标题查找
            title_patterns = ['微信', 'WeChat']
            for pattern in title_patterns:
                try:
                    window = auto.WindowControl(searchDepth=1, SubName=pattern)
                    if window.Exists(2):
                        print(f"✅ 通过标题找到微信窗口: {pattern}")
                        self.wechat_window = window
                        return window
                except:
                    continue
            
            # 方法3：枚举所有窗口
            print("🔍 枚举所有窗口查找微信...")
            desktop = auto.GetRootControl()
            for window in desktop.GetChildren():
                try:
                    window_name = window.Name
                    if window_name and ('微信' in window_name or 'WeChat' in window_name):
                        print(f"✅ 枚举找到微信窗口: {window_name}")
                        self.wechat_window = window
                        return window
                except:
                    continue
            
            print("❌ 未找到微信窗口")
            return None
            
        except Exception as e:
            print(f"❌ 查找微信窗口失败: {e}")
            return None
    
    def activate_wechat(self):
        """激活微信窗口"""
        if not self.wechat_window:
            self.wechat_window = self.find_wechat_window()
        
        if not self.wechat_window:
            return False
        
        try:
            print("🎯 激活微信窗口...")
            self.wechat_window.SetActive()
            self.wechat_window.SetTopmost(True)
            time.sleep(2)
            print("✅ 微信窗口已激活")
            return True
        except Exception as e:
            print(f"❌ 激活微信窗口失败: {e}")
            return False
    
    def send_link_to_wechat(self, article_url):
        """在微信中发送文章链接"""
        if not UI_AUTOMATION_AVAILABLE:
            print("❌ uiautomation不可用，请手动操作")
            return False
        
        try:
            print("📝 在微信中发送文章链接...")
            
            # 确保微信窗口激活
            if not self.activate_wechat():
                return False
            
            # 方法1：查找并点击文件传输助手（使用更短的搜索时间）
            print("📁 尝试查找文件传输助手...")
            try:
                # 查找文件传输助手（减少搜索深度和时间）
                file_transfer = auto.TextControl(searchDepth=3, Name='文件传输助手')
                if file_transfer.Exists(1):  # 减少等待时间
                    print("✅ 找到文件传输助手")
                    file_transfer.Click()
                    time.sleep(1)
                else:
                    print("⚠️ 未找到文件传输助手，尝试查找聊天列表...")
                    # 查找任何聊天项目（减少搜索深度）
                    chat_items = auto.ListItemControl(searchDepth=2)
                    if chat_items.Exists(1):  # 减少等待时间
                        chat_items.Click()
                        time.sleep(1)
                    else:
                        print("⚠️ 未找到聊天项目，跳过聊天选择")
            except Exception as e:
                print(f"⚠️ 查找聊天窗口失败: {e}")
                print("💡 将直接尝试查找输入框")
            
            # 方法2：查找输入框并输入链接（优化搜索）
            print("⌨️ 查找输入框...")
            input_box = None

            # 尝试多种方式查找输入框（减少搜索时间）
            input_patterns = [
                {'searchDepth': 3, 'ClassName': 'Edit'},
                {'searchDepth': 2, 'ControlType': auto.ControlType.EditControl},
            ]

            for pattern in input_patterns:
                try:
                    if 'ClassName' in pattern:
                        input_box = auto.EditControl(searchDepth=pattern['searchDepth'],
                                                   ClassName=pattern['ClassName'])
                    elif 'ControlType' in pattern:
                        input_box = auto.Control(searchDepth=pattern['searchDepth'],
                                               ControlType=pattern['ControlType'])

                    if input_box.Exists(1):  # 减少等待时间
                        print("✅ 找到输入框")
                        break
                    else:
                        input_box = None
                except Exception as e:
                    print(f"⚠️ 搜索输入框失败: {e}")
                    continue
            
            if not input_box:
                print("❌ 未找到输入框")
                return False
            
            # 点击输入框并输入链接
            print("📝 输入文章链接...")
            input_box.Click()
            time.sleep(0.5)
            
            # 清空输入框
            auto.SendKeys('{Ctrl}a')
            time.sleep(0.2)
            
            # 输入链接
            input_box.SendKeys(article_url)
            time.sleep(1)
            
            # 发送消息
            auto.SendKeys('{Enter}')
            time.sleep(2)
            
            print("✅ 文章链接已发送")
            return True
            
        except Exception as e:
            print(f"❌ 发送链接失败: {e}")
            return False
    
    def click_link_in_wechat(self):
        """点击微信中的文章链接"""
        try:
            print("🔗 查找并点击文章链接...")

            # 查找包含链接的元素
            link_patterns = [
                'mp.weixin.qq.com',
                '微信公众平台',
                '查看链接',
            ]
            
            for pattern in link_patterns:
                try:
                    link_element = auto.TextControl(searchDepth=5, SubName=pattern)
                    if link_element.Exists(3):
                        print(f"✅ 找到链接元素: {pattern}")
                        link_element.Click()
                        time.sleep(3)
                        return True
                except:
                    continue
            
            # 如果找不到特定链接，尝试查找任何可点击的链接
            try:
                clickable_elements = auto.Control(searchDepth=5, ControlType=auto.ControlType.HyperlinkControl)
                if clickable_elements.Exists(2):
                    print("✅ 找到可点击链接")
                    clickable_elements.Click()
                    time.sleep(3)
                    return True
            except:
                pass
            
            print("⚠️ 未找到可点击的链接，请手动点击")
            return False
            
        except Exception as e:
            print(f"❌ 点击链接失败: {e}")
            return False
    
    def refresh_wechat_browser(self, refresh_count=5, interval=3):
        """在微信内置浏览器中刷新页面"""
        if not UI_AUTOMATION_AVAILABLE:
            print("❌ uiautomation不可用")
            return False
        
        try:
            print(f"🔄 开始在微信内置浏览器中刷新页面 {refresh_count} 次...")
            
            # 查找微信浏览器窗口
            browser_window = self.find_wechat_browser_window()
            if not browser_window:
                print("⚠️ 使用微信主窗口进行刷新")
                browser_window = self.wechat_window
            
            if not browser_window:
                print("❌ 未找到可用窗口")
                return False
            
            for i in range(refresh_count):
                print(f"🔄 第 {i+1}/{refresh_count} 次刷新")
                
                try:
                    # 激活窗口
                    browser_window.SetActive()
                    time.sleep(1)
                    
                    # 尝试多种刷新方式
                    refresh_success = False
                    
                    # 方式1：F5刷新
                    try:
                        auto.SendKeys('{F5}')
                        print("   ✅ F5刷新已发送")
                        refresh_success = True
                    except:
                        pass
                    
                    # 方式2：Ctrl+R刷新
                    if not refresh_success:
                        try:
                            auto.SendKeys('{Ctrl}r')
                            print("   ✅ Ctrl+R刷新已发送")
                            refresh_success = True
                        except:
                            pass
                    
                    if refresh_success:
                        print(f"   ⏳ 等待 {interval} 秒...")
                        time.sleep(interval)
                    else:
                        print("   ❌ 刷新失败")
                        
                except Exception as e:
                    print(f"   ❌ 第 {i+1} 次刷新出错: {e}")
                    continue
            
            print("✅ 微信浏览器刷新完成")
            return True
            
        except Exception as e:
            print(f"❌ 微信浏览器刷新失败: {e}")
            return False
    
    def find_wechat_browser_window(self):
        """查找微信内置浏览器窗口"""
        if not UI_AUTOMATION_AVAILABLE:
            return None
        
        try:
            # 查找浏览器相关窗口
            browser_patterns = [
                {'ClassName': 'Chrome_WidgetWin_1', 'SubName': '微信'},
                {'ClassName': 'CefWebViewWnd'},
                {'SubName': 'mp.weixin.qq.com'},
            ]
            
            for pattern in browser_patterns:
                try:
                    if 'ClassName' in pattern and 'SubName' in pattern:
                        window = auto.WindowControl(searchDepth=1, 
                                                  ClassName=pattern['ClassName'],
                                                  SubName=pattern['SubName'])
                    elif 'ClassName' in pattern:
                        window = auto.WindowControl(searchDepth=1, 
                                                  ClassName=pattern['ClassName'])
                    elif 'SubName' in pattern:
                        window = auto.WindowControl(searchDepth=1, 
                                                  SubName=pattern['SubName'])
                    else:
                        continue
                    
                    if window.Exists(2):
                        print(f"✅ 找到微信浏览器窗口: {pattern}")
                        self.browser_window = window
                        return window
                        
                except:
                    continue
            
            return None
            
        except Exception as e:
            print(f"⚠️ 查找微信浏览器窗口失败: {e}")
            return None
    
    def simple_test(self):
        """简单测试微信连接"""
        print("🧪 简单测试微信连接...")

        # 检查环境
        if not self.check_uiautomation():
            return False

        # 查找微信窗口
        if not self.find_wechat_window():
            print("❌ 请先打开微信PC版")
            return False

        # 激活微信窗口
        if not self.activate_wechat():
            print("❌ 激活微信窗口失败")
            return False

        print("✅ 微信连接测试成功")
        return True

    def open_and_refresh_article(self, article_url, refresh_count=5):
        """完整的打开和刷新文章流程"""
        print("🚀 开始微信内置浏览器自动化流程...")
        print(f"🔗 文章链接: {article_url}")

        # 先进行简单测试
        if not self.simple_test():
            return False

        # 询问用户是否继续自动化
        print("\n💡 微信连接成功！现在将尝试自动发送链接...")
        print("⚠️ 如果自动化失败，程序会提示您手动操作")

        choice = input("是否继续自动化流程？(y/N): ").strip().lower()
        if choice != 'y':
            print("❌ 用户取消自动化")
            print("💡 请手动在微信中打开文章链接并刷新页面")
            return False

        # 发送链接
        try:
            if not self.send_link_to_wechat(article_url):
                print("⚠️ 自动发送链接失败，请手动发送")
                input("请手动在微信中发送文章链接，然后按回车继续...")
        except Exception as e:
            print(f"⚠️ 发送链接时出错: {e}")
            input("请手动在微信中发送文章链接，然后按回车继续...")

        # 点击链接
        print("🔗 尝试自动点击链接...")
        try:
            if not self.click_link_in_wechat():
                print("⚠️ 自动点击链接失败，请手动点击")
                input("请手动点击微信中的文章链接，等待页面加载完成后按回车继续...")
        except Exception as e:
            print(f"⚠️ 点击链接时出错: {e}")
            input("请手动点击微信中的文章链接，等待页面加载完成后按回车继续...")

        # 等待页面加载
        print("⏳ 等待页面加载...")
        time.sleep(5)

        # 刷新页面
        try:
            if not self.refresh_wechat_browser(refresh_count):
                print("⚠️ 自动刷新失败，请手动刷新")
                for i in range(refresh_count):
                    input(f"请手动刷新微信浏览器页面（第 {i+1}/{refresh_count} 次），然后按回车...")
        except Exception as e:
            print(f"⚠️ 刷新页面时出错: {e}")
            for i in range(refresh_count):
                input(f"请手动刷新微信浏览器页面（第 {i+1}/{refresh_count} 次），然后按回车...")

        print("✅ 微信内置浏览器自动化流程完成")
        return True
