# coding:utf-8
# main_enhanced.py
"""
微信公众号爬虫工具集 v2.4
集成改进的文章内容提取功能
功能：抓取Cookie和批量内容+统计抓取（支持隐藏内容提取）
"""

import os
import time
from read_cookie import ReadCookie
from batch_readnum_spider import BatchReadnumSpider
from excel_auto_crawler import ExcelAutoCrawler

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🚀 微信公众号爬虫工具集 v2.5")
    print("="*60)
    print("1. 抓取Cookie（首次使用或Cookie过期时）")
    print("2. 批量内容+统计抓取（推荐：内容+阅读量+点赞数+分享数）")
    print("3. 自动化Excel链接爬取（新功能）")
    print("4. 测试内容提取功能")
    print("5. 退出程序")
    print("="*60)
    print("💡 v2.5新特性：支持从Excel读取链接并自动化打开微信文章")

def extract_cookies():
    """抓取Cookie"""
    print("\n🔧 启动Cookie抓取器...")
    cookie_reader = ReadCookie()
    
    print("请选择操作:")
    print("1. 自动启动抓取器（需要手动访问微信公众号）")
    print("2. 只解析现有cookie文件")
    
    choice = input("请选择(1/2): ").strip()
    
    if choice == '1':
        # 启动抓取器
        if cookie_reader.start_cookie_extractor(timeout=120):
            print("\n抓取完成，开始解析...")
        else:
            print("抓取器启动失败")
            return False
    
    # 解析cookie
    result = cookie_reader.get_latest_cookies()
    
    if result:
        print("\n" + "="*50)
        print("✅ Cookie解析成功:")
        print(f"   __biz: {result['biz']}")
        print(f"   appmsg_token: {result['appmsg_token'][:20]}...")
        print(f"   解析时间: {result['timestamp']}")
        print("="*50)
        return True
    else:
        print("❌ Cookie解析失败，请确保:")
        print("1. 已正确访问微信公众号文章")
        print("2. 代理设置正确(127.0.0.1:8080)")
        print("3. wechat_keys.txt文件中有有效数据")
        return False

def batch_readnum_crawler():
    """批量文章内容+统计数据抓取"""
    print("\n📊 启动批量内容+统计抓取器（文章内容+阅读量+点赞数+分享数）...")
    
    # 检查cookie文件
    if not os.path.exists('wechat_keys.txt'):
        print("❌ 未找到cookie文件，请先抓取Cookie")
        return
    
    # 获取用户配置
    print("\n请配置抓取参数:")
    try:
        max_pages = int(input("最大页数 (默认3): ") or "3")
        articles_per_page = int(input("每页文章数 (默认10): ") or "10")
        days_back = int(input("抓取多少天内的文章 (默认7): ") or "7")
    except ValueError:
        print("❌ 参数输入无效，使用默认值")
        max_pages, articles_per_page, days_back = 3, 10, 7
    
    # 初始化爬虫
    spider = BatchReadnumSpider()
    
    try:
        print(f"\n🚀 开始批量抓取...")
        print(f"📋 配置: {max_pages}页 × {articles_per_page}篇/页，{days_back}天内文章")
        
        # 执行抓取
        results = spider.batch_crawl_readnum(
            max_pages=max_pages,
            articles_per_page=articles_per_page,
            days_back=days_back
        )
        
        if results:
            # 显示统计摘要
            spider.print_summary()
            
            # 保存数据
            print(f"\n💾 正在保存数据...")
            excel_file = spider.save_to_excel()
            json_file = spider.save_to_json()
            
            print(f"\n🎉 抓取完成！")
            if excel_file:
                print(f"📊 Excel文件: {excel_file}")
            if json_file:
                print(f"💾 JSON文件: {json_file}")
        else:
            print("❌ 未获取到任何数据，请检查Cookie是否有效")
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断抓取")
        if spider.articles_data:
            print("💾 保存已抓取的数据...")
            spider.save_to_excel()
            spider.save_to_json()
    except Exception as e:
        print(f"❌ 抓取过程出错: {e}")
        import traceback
        traceback.print_exc()

def excel_auto_crawler():
    """Excel自动化爬取功能"""
    print("\n🤖 启动Excel自动化爬取器...")

    # 检查uiautomation库
    try:
        import uiautomation as auto
        ui_available = True
        print("✅ uiautomation库已安装，支持完全自动化")
    except ImportError:
        ui_available = False
        print("⚠️ uiautomation库未安装")
        print("💡 建议安装以获得完全自动化体验：pip install uiautomation")

        choice = input("是否继续使用半自动模式？(y/N): ").strip().lower()
        if choice != 'y':
            print("❌ 用户取消操作")
            print("💡 可以运行 python install_and_test_ui.py 来安装和测试uiautomation库")
            return

    try:
        crawler = ExcelAutoCrawler()

        # 检查Excel文件是否存在
        if not os.path.exists(crawler.excel_file):
            print("📝 首次使用，正在创建示例Excel文件...")
            crawler.create_sample_excel()
            print("\n" + "="*60)
            print("📋 请按以下步骤操作：")
            print("1. 打开生成的 target_articles.xlsx 文件")
            print("2. 在'公众号名称'列填入要爬取的公众号名称")
            print("3. 在'示例文章链接'列填入对应公众号的任意一篇文章链接")
            print("4. 保存文件后重新运行此功能")
            print("="*60)
            return

        # 显示重要提醒
        print("\n" + "="*60)
        print("⚠️  重要提醒：")
        print("1. 必须配置浏览器代理为 127.0.0.1:8080")
        print("2. 必须在浏览器中打开微信文章并刷新才能抓到包")
        print("3. 程序将自动启动Cookie抓取器")
        if ui_available:
            print("4. 程序将自动打开链接并刷新页面")
        else:
            print("4. 需要手动刷新浏览器页面")
        print("5. 验证Cookie后开始批量爬取所有公众号")
        print("="*60)

        confirm = input("确认开始自动化流程？(y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 用户取消操作")
            return

        # 开始自动化爬取
        success = crawler.auto_crawl_from_excel()

        if success:
            print("\n🎉 Excel自动化爬取完成！")
        else:
            print("\n❌ Excel自动化爬取失败，请检查配置和网络")

    except Exception as e:
        print(f"❌ Excel自动化爬取出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主程序入口"""
    print("🎯 微信公众号爬虫工具集 v2.5")
    while True:
        show_menu()
        choice = input("\n请选择功能 (1-5): ").strip()
        if choice == '1':
            extract_cookies()
        elif choice == '2':
            batch_readnum_crawler()
        elif choice == '3':
            excel_auto_crawler()
        elif choice == '4':
            print("🧪 测试功能开发中...")
        elif choice == '5':
            print("👋 感谢使用，再见！")
            break
        else:
            print("❌ 无效选择，请输入1-5")

if __name__ == '__main__':
    main()
