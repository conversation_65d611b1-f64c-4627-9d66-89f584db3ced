# coding:utf-8
# test_wechat_simple.py
"""
简单测试微信自动化功能
"""

from wechat_browser_automation import WeChatBrowserAutomation

def main():
    print("🧪 简单测试微信自动化功能")
    print("="*40)
    
    # 创建自动化实例
    automation = WeChatBrowserAutomation()
    
    # 运行简单测试
    if automation.simple_test():
        print("\n✅ 微信自动化基础功能测试通过")
        print("💡 可以继续使用完整的自动化爬取功能")
    else:
        print("\n❌ 微信自动化基础功能测试失败")
        print("💡 请检查微信PC版是否已打开")
    
    print("="*40)

if __name__ == "__main__":
    main()
