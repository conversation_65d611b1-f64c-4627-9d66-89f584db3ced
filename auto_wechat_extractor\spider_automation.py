import uiautomation as auto
import time
import sys


def search_and_open_article(account_name: str, timeout=15):
    """
    自动化操作微信，搜索指定公众号并点击进入。
    采用更通用的查找策略，以适应微信版本变化。
    """
    print("正在查找微信窗口...")
    wechat_win = auto.WindowControl(searchDepth=1, ClassName='WeChatMainWndForPC')

    if not wechat_win.Exists(5, 1):
        print("错误：未找到微信窗口。请确保微信已登录并显示在主界面。")
        return

    print(f"成功链接到微信窗口: {wechat_win.Name}")
    wechat_win.SetActive()
    time.sleep(1)

    print("步骤 1: 点击并清空搜索框...")
    search_box = wechat_win.EditControl(Name='搜索')
    if not search_box.Exists(3, 1):
        print("错误：未找到搜索框。")
        return
    search_box.Click(simulateMove=False)
    time.sleep(0.5)
    search_box.SendKeys('{Ctrl}a{Delete}', interval=0.1)
    time.sleep(0.5)

    print(f"步骤 2: 输入搜索内容 '{account_name}' 并回车...")
    search_box.SendKeys(account_name, interval=0.1)
    time.sleep(1.5)
    search_box.SendKeys('{Enter}')
    time.sleep(3)  # 等待搜索结果加载

    print("步骤 3: 在搜索结果中查找公众号...")
    gzh_target = wechat_win.Control(
        searchDepth=15,
        RegexName=f".*{account_name}.*公众号.*"
    )

    start_time = time.time()
    while time.time() - start_time < timeout:
        if gzh_target.Exists(0.5, 0.1):
            print(f"成功找到公众号 '{account_name}' 的目标区域！")
            try:
                gzh_target.Click(simulateMove=False, waitTime=1)
                print("步骤 4: 点击操作完成。")

                # === 新策略：主动验证并打印新页面信息 ===
                print("\n" + "="*15 + " 开始验证新页面加载 " + "="*15)
                print("等待公众号主页加载，特征是查找“...”按钮...")
                time.sleep(2) # 初始等待

                # 在微信主窗口中查找“更多”按钮，这是公众号主页的稳定特征
                more_button = wechat_win.ButtonControl(Name="…")
                
                page_load_timeout = 10
                start_verify_time = time.time()
                page_loaded = False
                while time.time() - start_verify_time < page_load_timeout:
                    if more_button.Exists(0.5, 0.1):
                        print("成功验证：在页面上找到了“...”按钮，确认已进入公众号主页。")
                        page_loaded = True
                        break
                    else:
                        print("正在等待页面加载...")
                
                if page_loaded:
                    print("\n" + "="*15 + " 开始打印新页面信息 " + "="*15)
                    print("当前页面控件结构如下：")
                    # 既然已确认页面加载，我们打印整个窗口的结构以供分析
                    auto.EnumAndLogControl(wechat_win, maxDepth=8) # 使用更深的深度
                    print("="*17 + " 新页面信息打印完毕 " + "="*17 + "\n")
                else:
                    print(f"!!! 错误：在 {page_load_timeout} 秒内未能等到公众号主页加载（未找到“...”按钮）。")

                # =======================================

                return  # 操作成功，退出函数
            except Exception as e:
                print(f"尝试点击或分析新页面时发生错误: {e}")
                break
        else:
            print(f"未能立即找到，正在重试... (已用时 {int(time.time() - start_time)}s)")

    print(f"!!! 最终失败：在 {timeout} 秒内未能找到公众号 '{account_name}'。")


if __name__ == '__main__':
    # ==================================================
    #  从配置文件 accounts.txt 中读取要操作的公众号列表
    try:
        with open('accounts.txt', 'r', encoding='utf-8') as f:
            target_accounts = [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        print("错误：未找到公众号列表文件 'accounts.txt'。")
        print("请创建一个 accounts.txt 文件，每行输入一个公众号名称。")
        exit()

    if not target_accounts:
        print("'accounts.txt' 文件为空，请输入至少一个公众号名称。")
        exit()

    print(f"成功读取到 {len(target_accounts)} 个公众号，准备开始操作...")
    # ==================================================

    for account in target_accounts:
        print(f"\n{'='*20} 正在处理公众号: {account} {'='*20}")
        search_and_open_article(account)
        # 在处理下一个公众号之前，可以加入一个延时，给微信一些反应时间
        # 例如，等待5秒，让公众号页面完全加载，或者返回到主界面
        print("...等待 5 秒后继续下一个任务...")
        time.sleep(5)

    print(f"\n{'='*20} 所有公众号处理完毕 {'='*20}")